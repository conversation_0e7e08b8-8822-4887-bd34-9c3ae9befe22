import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useState, useEffect } from 'react';

import { DateTimePicker } from '@mui/x-date-pickers';

import { useCountryContext } from '../../context/country';

dayjs.extend(utc);
dayjs.extend(timezone);

export default function GdxDateTimePicker({ label, value, onChange, ...restProps }) {
  const { timezone: tz } = useCountryContext();
  const [v, setV] = useState(null);
  useEffect(() => {
    if (value) {
      setV(dayjs.tz(value, tz));
    } else {
      setV(null);
    }
  }, [value, tz]);
  return (
    <DateTimePicker
      label={label}
      value={v}
      onChange={(nv) => {
        if (onChange) {
          onChange(nv ? nv.valueOf() : null);
        }
      }}
      timezone={tz}
      slotProps={{
        field: { clearable: true },
      }}
      sx={{
        maxWidth: { xs: 1, md: 200 },
      }}
      {...restProps}
    />
  );
}
