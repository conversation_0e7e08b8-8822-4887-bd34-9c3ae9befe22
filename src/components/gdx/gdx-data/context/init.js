
export function initData({ register, rl }) {
    register('country-all', {
        type: 'url',
        url: '/api/country/list',
    });
    register('country', {
        type: 'redirect',
        sourceType: 'country-all',
        map: (x) => (x.enabled ? x : null)
    });
    register('lang', {
        type: 'url',
        url: '/api/language/list',
        query: { query: { enabled: true } }
    })
    register('langCode', {
        type: 'redirect',
        sourceType: 'lang',
        map: (x) => ({ id: x.mainCode, name: x.name })
    })
    register('enable-country', {
        type: 'redirect',
        sourceType: 'country-all',
        map: (x) => (x.enabled ? x : null)
    })
    register('commission-type', {
        type: 'options',
        options: [
            { id: 'Fixed', name: rl('Fixed Commission'), color: 'default' },
            { id: 'Proportional', name: rl('Proportional Commission'), color: 'primary' }
        ]
    });
    register('gb-cash-type', {
        type: 'options',
        options: [
            { id: 'RECHARGE', name: rl('Recharge'), color: 'success' },
            { id: 'GAME_COST', name: rl('Game Cost'), color: 'default' },
            { id: 'MANUAL_COST', name: rl('Manual Cost'), color: 'default' },
            { id: 'RELEASE_COST', name: rl('Release Cost'), color: 'default' },
            { id: 'ADD_COUNTRY_COST', name: rl('Add Country Cost'), color: 'default' },
            { id: 'MONTH_SERVICE_COST', name: rl('Month Service Cost'), color: 'default' },
            { id: 'MONTH_GUARANTEED_COST', name: rl('Month Guaranteed Cost'), color: 'default' },
        ]
    })

    register('adx-cash-type', {
        type: 'options',
        options: [
            { id: 'ADX_COMMISSION', name: rl('Commission'), color: 'success' },
            { id: 'WITHDRAW', name: rl('Withdraw'), color: 'red' },
            { id: 'WITHDRAW_ROLLBACK', name: rl('Withdraw Rollback'), color: 'orange' },
        ]
    })

    register('view-page-type', {
        type: 'type-define',
        tdcId: 'VIEW_PAGE_TYPE'
    })
    register('cash-type', {
        type: 'type-define',
        tdcId: 'CASH_TYPE'
    })
    register('theme-template', {
        type: 'url',
        url: '/api/gb_theme_template/list'
    })
    register('com', {
        type: 'url',
        url: '/api/bix/view_component/list',
    })
    register('com-tab', {
        type: 'redirect',
        sourceType: 'com',
        map: (x) => (x.types && x.types.indexOf('TAB') >= 0 ? x : false)
    })
    register('com-menu', {
        type: 'redirect',
        sourceType: 'com',
        map: (x) => (x.types && x.types.indexOf('MENU') >= 0 ? x : false)
    })
    register('com-block', {
        type: 'redirect',
        sourceType: 'com',
        map: (x) => (x.types && x.types.indexOf('BLOCK') >= 0 ? x : false)
    })
    register('game-platform', {
        type: 'url',
        url: '/api/game_platform/list',
    })
    register('sys-game-tag', {
        type: 'url',
        url: '/api/bix/sys_game_tag/list'
    })
    register('game-tag', {
        type: 'url',
        url: '/api/game_tag/list'
    })
    register('app', {
        type: 'url',
        url: '/api/app/list'
    })
    register('design-device', {
        type: 'options',
        options: [
            { id: 'mobile', name: rl('Mobile'), color: 'success' },
            { id: 'pc', name: rl('PC'), color: 'error' }
        ]
    })
    register('brand', {
        type: 'url',
        url: '/api/brand/list'
    })
    register('design-theme-config', {
        type: 'url',
        url: '/api/design_theme_config/list'
    })
    register('enabled', {
        type: 'options',
        options: [
            { id: true, name: rl('Enabled'), color: 'success' },
            { id: false, name: rl('Disabled'), color: 'error' }
        ]
    })
    register('online', {
        type: 'options',
        options: [
            { id: true, name: rl('Online'), color: 'success' },
            { id: false, name: rl('Offline'), color: 'error' }
        ]
    })
    register('enabledStr', {
        type: 'options',
        options: [
            { id: 'true', name: rl('Enabled'), color: 'success' },
            { id: 'false', name: rl('Disabled'), color: 'error' }
        ]
    })
    register('disabled', {
        type: 'options',
        options: [
            { id: false, name: rl('Enabled'), color: 'success' },
            { id: true, name: rl('Disabled'), color: 'error' }
        ]
    })
    register('boolean', {
        type: 'options',
        options: [
            { id: true, name: rl('Y'), color: 'success' },
            { id: false, name: rl('N'), color: 'error' }
        ]
    })
    register('booleanStr', {
        type: 'options',
        options: [
            { id: 'true', name: rl('Y'), color: 'success' },
            { id: 'false', name: rl('N'), color: 'error' }
        ]
    })
    register('hit', {
        type: 'options',
        options: [
            { id: true, name: rl('Hit'), color: 'success' }
        ]
    })
    register('agent-config', {
        type: 'url',
        url: '/api/v2/agent_config/list',
        watch: ['/api/v2/agent_config/persist']
    })
    register('account-role', {
        type: 'url',
        url: '/api/role/list'
    })
    register('permission', {
        type: 'url',
        url: '/api/permission/list'
    })
    register('font', {
        type: 'url',
        url: '/api/font_config/list'
    })
    register('pay', {
        type: 'url',
        url: '/api/pay/list'
    })
    register('payMode', {
        type: 'options',
        options: [
            { id: 'EMBED', name: rl('Embedded'), color: 'blue' },
            { id: 'OPEN_WINDOW', name: rl('External Open'), color: 'orange' }
        ]
    })
    register('pay-strategy', {
        type: 'options',
        options: [
            { id: 'PRIORITY', name: rl('Priority Strategy'), },
            { id: 'SMART', name: rl('Intelligent Switching Strategy'), color: 'blue' },
            { id: 'WEIGHT', name: rl('Weights Strategy'), color: 'orange' }
        ]
    })
    register('currency', {
        type: 'url',
        url: '/api/currency/list',
        query: { query: { enabled: true } }
    })
    register('app-type', {
        type: 'options',
        options: [
            { id: 'H5', name: rl('H5'), color: '#2165d1' },
            { id: 'BRAND_APP', name: rl('APK'), color: '#22c55d' }
        ]
    })
    register('email-template', {
        type: 'url',
        url: '/api/email_template/list',
    })
    register('user-tag', {
        type: 'url',
        url: '/api/user_tag/list',
    })
    register('test', {
        type: 'options',
        options: [
            { id: false, name: rl('Formal'), color: 'success' },
            { id: true, name: rl('Test'), color: 'error' }
        ]
    })
    register('agent-strategy-type', {
        type: 'options',
        options: [
            { id: 'TYPE1', name: rl('Triple-level Proxy Mode'), color: '#2165d1' },
            { id: 'TYPE2', name: rl('Infinite-level Proxy Profit Margin Model'), color: '#22c55d' }
        ]
    })
    register('recharge-status', {
        type: 'options',
        options: [
            { id: 'PENDING', name: rl('PENDING'), color: 'orange' },
            { id: 'SUCCESS', name: rl('SUCCESS'), color: 'green' },
            { id: 'FAILED', name: rl('FAILED'), color: 'red' },
        ]
    })
    register('recharge-result-status', {
        type: 'redirect',
        sourceType: 'recharge-status',
        map: (x) => (x.id !== 'PENDING' ? x : false)
    })
    register('terminal', {
        type: 'options',
        options: [
            { id: 'BROWSER', name: rl('Browser'), color: 'blue' },
            { id: 'APP', name: rl('APP'), color: 'orange' },
            { id: 'GP_APP', name: rl('GP_APP'), color: 'green' }
        ]
    })
    register('pay-type', {
        type: 'url',
        url: '/api/pay/pay_type_list',
    })
    register('pay-type-currencyId', {
        type: 'redirect',
        sourceType: 'pay-type',
        map: (x) => (x && x.currencyId)
    })
    register('email-type', {
        type: 'options',
        options: [
            { id: 'FORGOT_PASSWORD', name: rl('Forgot Password'), color: 'blue' },
            { id: 'REGISTER_VERIFY', name: rl('Register Verify'), color: 'orange' },
            { id: 'WITHDRAW_VERIFY', name: rl('Withdraw Verify'), color: 'green' },
            { id: 'ADX_FB_ACCOUNT_APPROVE', name: rl('Adx FB Account Bind'), color: 'purple' },
        ]
    })
    register('cash-back', {
        type: 'options',
        options: [
            { id: 'ALL', name: rl('All'), color: 'blue' },
            { id: 'PLATFORM', name: rl('Platform'), color: 'orange' },
            { id: 'GAME', name: rl('Game'), color: 'green' }
        ]
    })
    register('game', {
        type: 'url',
        url: '/api/game/list',
    })
    register('p_game', {
        type: 'url',
        url: '/api/game/show_list',
    })
    register('game-category-type', {
        type: 'options',
        options: [
            { id: 'ALL', name: rl('All'), color: 'blue' },
            { id: 'CATEGORY', name: rl('Category'), color: 'blue' },
            { id: 'GAME', name: rl('Game'), color: 'orange' },
            { id: 'LOTTERY', name: rl('Lottery'), color: '#7427b5' },
            { id: 'LINK', name: rl('Link'), color: 'red' },
            { id: 'FAVOR', name: rl('Collection Game'), color: 'green' },
            { id: 'FILTER', name: rl('Custom Filter'), color: '#eccc68' },
            { id: 'CHATROOM', name: rl('Chat Room'), color: '#ff7c2c' }
        ]
    })
    register('account', {
        type: 'url',
        url: '/api/account/accounts'
    })
    register('game-category', {
        type: 'url',
        url: '/api/game_category/list',
        query: { query: { childrenFlag: false } }
    })
    register('design-game-category', {
        type: 'url',
        url: '/api/design_game_category/list',
        query: { query: { childrenFlag: false } }
    })
    register('game-tag', {
        type: 'url',
        url: '/api/game_tag/list',
    })
    register('bill-status', {
        type: 'options',
        options: [
            { id: true, name: rl('Already Generated Bill'), color: 'success' },
            { id: false, name: rl('Not Yet Generated Bill'), color: 'error' },
        ]
    })
    register('customer', {
        type: 'url',
        url: '/api/customer/list',
        query: { query: {} }
    })
    register('wk-order-type', {
        type: 'url',
        url: '/api/wk-order-type/list',
    })
    register('email-status', {
        type: 'options',
        options: [
            { id: 'NEW', name: rl('PENDING'), color: 'orange' },
            { id: 'SUCCESS', name: rl('SUCCESS'), color: 'green' },
            { id: 'FAILED', name: rl('FAILED'), color: 'red' },
        ]
    })
    register('email_type', {
        type: 'options',
        options: [
            { id: 'SYSTEM', name: rl('SYSTEM'), color: 'orange' },
            { id: 'QA', name: rl('QA'), color: 'blue' },
            { id: 'USER', name: rl('USER'), color: 'success' },
        ]
    })
    register('user_notice_type', {
        type: 'url',
        url: '/api/user_notice_type/list',
    })
    register('report_interval', {
        type: 'options',
        options: [
            { id: 'DAY', name: 'Day', color: 'green' },
            { id: 'WEEK', name: 'Week', color: 'blue' },
            { id: 'MONTH', name: 'Month', color: 'orange' }
        ]
    })
    register('report_type', {
        type: 'options',
        options: [
            { id: 'WEEK_SUMMARY', name: rl('Week'), color: 'blue' },
            { id: 'MONTH_SUMMARY', name: rl('Month'), color: 'orange' }
        ]
    })
    register('gb_domain', {
        type: 'url',
        url: '/api/bix/gb_domain/list',
        query: { query: {} }
    })
    register('theme_publish_task_status', {
        type: 'options',
        options: [
            { id: 'NEW', name: rl('NEW'), color: 'gray' },
            { id: 'SUCCESS', name: rl('SUCCESS'), color: 'green' },
            { id: 'FAILED', name: rl('FAILED'), color: 'red' },
            { id: 'CANCELED', name: rl('CANCELED'), color: 'orange' }

        ]
    })
    register('adx_fb_bind_status', {
        type: 'options',
        options: [
            { id: 'PENDING', name: rl('PENDING'), color: 'gray' },
            { id: 'WAITING_APPLY', name: rl('WAITING APPLY'), color: 'grey' },
            { id: 'WAITING_APPROVED', name: rl('WAITING APPROVED'), color: 'orange' },
            { id: 'CANCELED', name: rl('CANCELED'), color: 'purple' },
            { id: 'SUCCESS', name: rl('SUCCESS'), color: 'green' },
            { id: 'FAILED', name: rl('FAILED'), color: 'red' }

        ]
    })

    register('adx_fb_card_status', {
        type: 'options',
        options: [
            { id: 'PENDING', name: rl('PENDING'), color: 'gray' },
            { id: 'ASSIGNED', name: rl('ASSIGNED'), color: 'orange' },
            { id: 'SUCCESS', name: rl('SUCCESS'), color: 'green' },
            { id: 'FAILED', name: rl('FAILED'), color: 'red' }

        ]
    })

    register('adx_adtrace_status', {
        type: 'options',
        options: [
            { id: 'PENDING', name: rl('PENDING'), color: 'gray' },
            { id: 'STARTED', name: rl('STARTED'), color: 'orange' },
            { id: 'FINISHED', name: rl('FINISHED'), color: 'green' },

        ]
    })



    register('adx_adlink_action', {
        type: 'options',
        options: [
            { id: 'js', name: rl('js'), color: 'gray' },
            { id: 'sleep', name: rl('sleep'), color: 'orange' },
            { id: 'sleepRandom', name: rl('sleepRandom'), color: 'green' },
            { id: 'sleepAndJs', name: rl('sleepAndJs'), color: 'purple' }

        ]
    })

    register('adx_adlink', {
        type: 'url',
        url: '/api/adx_ad_link/list',
        query: { query: {} }
    })

    register('adx_app', {
        type: 'url',
        url: '/api/adx_app/list',
        query: { query: {} }
    })

    register('adx_fb_bm', {
        type: 'url',
        url: '/api/adx_fb_bm/list',
        query: { query: {} }
    })

    register('adx_credit_card', {
        type: 'url',
        url: '/api/adx_credit_card/list',
        query: { query: {} }
    })

    register('adx_sdk', {
        type: 'url',
        url: '/api/adx_sdk/select_list',
        query: { query: {} }
    })

    register('love_status', {
        type: 'options',
        options: [
            { id: "UNFINISHED", name: rl('未完成'), color: 'orange' },
            { id: "FINISHED", name: rl('已完成'), color: 'green' },

        ]
    })
}
