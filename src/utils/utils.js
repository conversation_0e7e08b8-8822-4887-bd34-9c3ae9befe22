export const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

export function objectFilterEmpty(obj) {
    return Object.fromEntries(
        Object.entries(obj).filter(([key, value]) => value !== null && value !== undefined && value !== '')
    );
}

export function isExternalLink(url) {
    return url?.startsWith('http');
}


export const hasParams = (url) => {
    if (!url) return false;
    const queryString = url.split('?')[1];
    return queryString ? new URLSearchParams(queryString).toString().length > 0 : false;
};


export function removeLastSlash(pathname) {
    /**
     * Remove last slash
     * [1]
     * @input  = '/dashboard/calendar/'
     * @output = '/dashboard/calendar'
     * [2]
     * @input  = '/dashboard/calendar'
     * @output = '/dashboard/calendar'
     */
    if (pathname !== '/' && pathname.endsWith('/')) {
        return pathname.slice(0, -1);
    }

    return pathname;
}


export function removeParams(url) {
    try {
        const urlObj = new URL(url, window.location.origin);

        return removeLastSlash(urlObj.pathname);
    } catch (error) {
        return url;
    }
}