import GdxDatePicker from 'src/components/gdx/gdx-data/components/GdxDatePicker';
import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';
import GdxImageUploader from 'src/components/gdx/gdx-data/components/GdxImageUploader';
import GdxInput from 'src/components/gdx/gdx-data/components/GdxInput';
import { GdxDefaultSelect } from 'src/components/gdx/gdx-data/components/GdxSelect';
import GdxFormItem from 'src/components/gdx/gdx-form/components/GdxFormItem';
import RemoteTable from 'src/components/gdx/remote-table';

export default function Page() {
  return (
    <>
      <GdxHelmet title="清单列表" />

      <RemoteTable
        options={{
          defaultPageSize: 20,
          baseUrl: '/api/love',
          topActions: ['new'],
          actions: ['edit', 'delete'],
          filters: [
            { id: 'title', label: '标题', type: 'text' },
            { id: 'status', label: '状态', type: 'select', dataType: 'love_status' },
          ],
          expandFilterStart: { xs: 1, md: null },
          columns: [
            { id: 'title', name: '标题' },
            { id: 'status', name: '状态', type: 'love_status' },
            { id: 'finishTime', name: '完成时间', type: 'date' },
          ],
          editFormColumn: 1,
          editForm: <EditFormItems></EditFormItems>,
        }}
      />
    </>
  );
}

function EditFormItems() {
  return (
    <>
      <GdxFormItem name="title">
        <GdxInput label="标题" />
      </GdxFormItem>
      <GdxFormItem name="status">
        <GdxDefaultSelect label="状态" dataType="love_status" />
      </GdxFormItem>
      <GdxFormItem name="finishTime">
        <GdxDatePicker label="完成时间" />
      </GdxFormItem>
      <GdxFormItem name="yLog">
        <GdxInput label="小徐日志" multiline rows={3} />
      </GdxFormItem>
      <GdxFormItem name="xLog">
        <GdxInput label="小张日志" multiline rows={3} />
      </GdxFormItem>
      <GdxFormItem name="photos">
        <GdxImageUploader label="照片" multiple />
      </GdxFormItem>
    </>
  );
}
