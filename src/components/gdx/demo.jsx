import { fNumber } from 'src/utils/format-number';
import GdxDatePicker from './gdx-data/components/GdxDatePicker';
import GdxImageUploader from './gdx-data/components/GdxImageUploader';
import GdxInput from './gdx-data/components/GdxInput';
import GdxInputNumber from './gdx-data/components/GdxInputNumber';
import GdxInputTags from './gdx-data/components/GdxInputTags';
import GdxSelect from './gdx-data/components/GdxSelect';
import GdxSwitch from './gdx-data/components/GdxSwitch';
import { useLangTranslate } from './gdx-data/context/common';
import GdxFormItem from './gdx-form/components/GdxFormItem';
import RemoteTable from './remote-table';
import { useRemoteTableContext } from './remote-table/context/hook';
import { Yupx } from './gdx-form';

export function Demo() {
  return (
    <RemoteTable
      options={{
        baseUrl: '/api/example', // API 基础路径
        defaultPageSize: 10, // 默认每页显示数量
        pages: [10, 20, 50, 100], // 可选的每页数量
        orderBy: 'createdTime', // 默认排序字段
        order: 'desc', // 默认排序方向

        // 表格列配置
        columns: [
          { id: 'id', name: '编号', align: 'center' },
          { id: 'name', name: '名称', sorter: true },
          { id: 'type', name: '类型', type: 'select', dataType: 'example-type' },
          { id: 'amount', name: '金额', type: 'amount', align: 'right' },
          { id: 'enabled', name: '状态', type: 'enabled', align: 'center' },
          { id: 'created', name: '创建时间', type: 'datetime' },
          {
            id: 'custom',
            name: '自定义',
            render: ({ value, row }) => `自定义内容: ${value}`,
          },
        ],

        // 操作按钮
        topActions: ['new', 'export'], // 顶部操作按钮
        actions: ['edit', 'delete', 'view'], // 行操作按钮
        batchActions: ['delete', 'enable', 'disable'], // 批量操作按钮

        // 筛选条件
        filters: [
          { id: 'name', label: '名称', type: 'text' },
          { id: 'type', label: '类型', type: 'select', dataType: 'example-type' },
          { id: 'dateRange', label: '日期范围', type: 'date-range' },
        ],
        expandFilterStart: 2, // 展开筛选项起始位置

        // 固定查询和持久化参数
        fixedQuery: { status: 'active' }, // 固定查询参数
        fixedPersist: { createdBy: 'currentUser' }, // 固定持久化参数

        // 编辑表单配置
        editForm: <EditFormItems />, // 编辑表单组件
        editFormColumn: 1, // 编辑表单列数
        editFormWidth: 600, // 编辑表单宽度
        editFormValidators: {
          // 表单验证规则
          name: Yupx.string().required('名称不能为空'),
        },
        editFormProps: {
          // 表单属性
          helperText: '编辑表单说明文字',
        },

        // 回调函数
        onPersist: (model, context) => 
          // 保存前处理
           ({ ...model, extraField: 'value' })
        ,
        onPersistAfter: (model, context) => {
          // 保存后处理
          console.log('保存成功', model);
        },
        onDeleteAfter: (model, context) => {
          // 删除后处理
          console.log('删除成功', model);
        },
        onAction: async (action, row, context) => {
          // 行操作处理
          console.log('执行操作', action, row);
          return true;
        },
        onBatchAction: async (action, context) => {
          // 批量操作处理
          console.log('执行批量操作', action);
          return true;
        },

        // 其他配置
        size: 'small', // 表格大小
        rowHeight: 60, // 行高
        limitHeight: 600, // 表格高度限制
        hidePage: false, // 是否隐藏分页
        hideToolbar: false, // 是否隐藏工具栏
        background: '#f5f5f5', // 背景色

        // 扩展行配置
        expandRowRender: (row, rowIndex) => 
          // 扩展行渲染
           <div>扩展行内容: {row.name}</div>
        ,
        canExpandRow: (row) => row.hasDetails, // 是否可展开行

        // 状态筛选配置
        statusFilter: {
          id: 'status',
          showAll: true,
        },

        // 请求钩子
        // requestHook: {
        //   apiPost: customApiPost,
        // },

        // 底部渲染
        footerRender: () => <div>表格底部内容</div>,
      }}
    />
  );
}



function EditFormItems() {
    const { rl } = useLangTranslate();
    const { editRecord, editAction } = useRemoteTableContext();
    
    // 根据不同的编辑操作类型返回不同的表单项
    if (editAction === 'new') {
        return (
            <>
                <GdxFormItem name="name" required>
                    <GdxInput label={rl('Name')} placeholder={rl('Please input name')}></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="code" required>
                    <GdxInput label={rl('Code')} placeholder={rl('Please input code')}></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="type" required defaultValue="normal">
                    <GdxSelect
                        label={rl('Type')} 
                        dataType="example-type" 
                        placeholder={rl('Please select type')}
                    ></GdxSelect>
                </GdxFormItem>
                <GdxFormItem name="amount" required defaultValue={0}>
                    <GdxInputNumber
                        label={rl('Amount')} 
                        min={0} 
                        placeholder={rl('Please input amount')}
                    ></GdxInputNumber>
                </GdxFormItem>
                <GdxFormItem name="description">
                    <GdxInput 
                        label={rl('Description')} 
                        multiline 
                        rows={3} 
                        placeholder={rl('Please input description')}
                    ></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="tags">
                    <GdxInputTags
                        label={rl('Tags')} 
                        placeholder={rl('Please input tags')}
                    ></GdxInputTags>
                </GdxFormItem>
                <GdxFormItem name="image">
                    <GdxImageUploader
                        label={rl('Image')} 
                        maxSize={2 * 1024 * 1024}
                    ></GdxImageUploader>
                </GdxFormItem>
                <GdxFormItem name="enabled" >
                    <GdxSwitch label={rl('Enabled')}></GdxSwitch>
                </GdxFormItem>
                <GdxFormItem name="date" defaultValue={new Date()}>
                    <GdxDatePicker label={rl('Date')}></GdxDatePicker>
                </GdxFormItem>
            </>
        );
    }
    
    // 编辑模式
    if (editAction === 'edit') {
        return (
            <>
                <GdxFormItem name="name" required>
                    <GdxInput label={rl('Name')}></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="code">
                    <GdxInput label={rl('Code')} disabled></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="type">
                    <GdxSelect label={rl('Type')} dataType="example-type"></GdxSelect>
                </GdxFormItem>
                <GdxFormItem name="amount">
                    <GdxInputNumber 
                        label={rl('Amount')} 
                        min={0}
                        helperText={`${rl('Current')}: ${fNumber(editRecord.amount || 0)}`}
                    ></GdxInputNumber>
                </GdxFormItem>
                <GdxFormItem name="description">
                    <GdxInput label={rl('Description')} multiline rows={3}></GdxInput>
                </GdxFormItem>
                <GdxFormItem name="tags">
                    <GdxInputTags label={rl('Tags')}></GdxInputTags>
                </GdxFormItem>
                <GdxFormItem name="image">
                    <GdxImageUploader label={rl('Image')} maxSize={2 * 1024 * 1024}></GdxImageUploader>
                </GdxFormItem>
                <GdxFormItem name="enabled">
                    <GdxSwitch label={rl('Enabled')}></GdxSwitch>
                </GdxFormItem>
                
                {/* 条件渲染表单项 */}
                {editRecord.type === 'special' && (
                    <GdxFormItem name="specialConfig">
                        <GdxInput label={rl('Special Config')}></GdxInput>
                    </GdxFormItem>
                )}
                
                {/* 高级选项分组 */}
                {/* <GdxFormGroup title={rl('Advanced Options')} defaultExpanded={false}>
                    <GdxFormItem name="priority" defaultValue={0}>
                        <GdxInputNumber label={rl('Priority')} min={0} max={100}></GdxInputNumber>
                    </GdxFormItem>
                    <GdxFormItem name="expireDate">
                        <GdxDatePicker label={rl('Expire Date')}></GdxDatePicker>
                    </GdxFormItem>
                </GdxFormGroup> */}
            </>
        );
    }
    
    // 批量编辑模式
    if (editAction === 'batch_edit') {
        return (
            <>
                <GdxFormItem name="type">
                    <GdxSelect label={rl('Type')} dataType="example-type"></GdxSelect>
                </GdxFormItem>
                <GdxFormItem name="enabled">
                    <GdxSwitch label={rl('Enabled')}></GdxSwitch>
                </GdxFormItem>
            </>
        );
    }
    
    // 默认返回空表单
    return null;
}