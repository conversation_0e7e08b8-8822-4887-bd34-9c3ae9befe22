import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/config-global';

import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

const icon = (name) => <SvgColor src={`${CONFIG.site.basePath}/assets/icons/navbar/${name}.svg`} />;

const ICONS = {
  job: icon('ic-job'),
  blog: icon('ic-blog'),
  chat: icon('ic-chat'),
  mail: icon('ic-mail'),
  user: icon('ic-user'),
  file: icon('ic-file'),
  lock: icon('ic-lock'),
  tour: icon('ic-tour'),
  order: icon('ic-order'),
  label: icon('ic-label'),
  blank: icon('ic-blank'),
  kanban: icon('ic-kanban'),
  folder: icon('ic-folder'),
  course: icon('ic-course'),
  banking: icon('ic-banking'),
  booking: icon('ic-booking'),
  invoice: icon('ic-invoice'),
  product: icon('ic-product'),
  calendar: icon('ic-calendar'),
  disabled: icon('ic-disabled'),
  external: icon('ic-external'),
  menuItem: icon('ic-menu-item'),
  ecommerce: icon('ic-ecommerce'),
  analytics: icon('ic-analytics'),
  dashboard: icon('ic-dashboard'),
  parameter: icon('ic-parameter'),
  game: icon('ic-game'),
  account: icon('ic-account'),
  home: icon('ic-home'),
  carousel: icon('ic-carousel'),
  love: icon('ic-love'),
};

// ----------------------------------------------------------------------

export const navData = [
  /**
   * Overview
   */
  // {
  //   subheader: 'Overview',
  //   items: [
  //     { title: 'App', path: paths.dashboard.root, icon: ICONS.dashboard },
  // { title: 'Ecommerce', path: paths.dashboard.general.ecommerce, icon: ICONS.ecommerce },
  // { title: 'Analytics', path: paths.dashboard.general.analytics, icon: ICONS.analytics },
  // { title: 'Banking', path: paths.dashboard.general.banking, icon: ICONS.banking },
  // { title: 'Booking', path: paths.dashboard.general.booking, icon: ICONS.booking },
  // { title: 'File', path: paths.dashboard.general.file, icon: ICONS.file },
  // { title: 'Course', path: paths.dashboard.general.course, icon: ICONS.course },
  //   ],
  // },
  /**
   * Management
   */
  {
    subheader: '管理',
    items: [
      { title: '首页', path: paths.dashboard.root, icon: ICONS.home },
      {
        title: '清单管理',
        path: paths.dashboard.love.list,
        icon: ICONS.love,
      },
      {
        title: '用户管理',
        path: paths.dashboard.user.list,
        icon: ICONS.user,
        children: [{ title: '用户列表', path: paths.dashboard.user.list }],
      },
      {
        title: '账户管理',
        path: paths.dashboard.account.list,
        icon: ICONS.account,
        children: [{ title: '账户列表', path: paths.dashboard.account.list }],
      },
      {
        title: '游戏管理',
        path: paths.dashboard.game.list,
        icon: ICONS.game,
        children: [{ title: '游戏列表', path: paths.dashboard.game.list }],
      },
      {
        title: '轮播管理',
        path: paths.dashboard.carousel.list,
        icon: ICONS.carousel,
        children: [{ title: '轮播图列表', path: paths.dashboard.carousel.list }],
      },
    ],
  },
];
