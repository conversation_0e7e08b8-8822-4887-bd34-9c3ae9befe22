import { Card } from '@mui/material';

import GdxTableFooter from './parts/GdxTableFooter';
import GdxTableToolbar from './parts/GdxTableToolbar';
import { RemoteTableProvider } from './context/provider';
import GdxFilterResults from './parts/GdxFilterRresults';
import GdxTableContainer from './parts/GdxTableContainer';
import GdxTableStatusBar from './parts/GdxTableStatusBar';
import GdxTableEditDialog from './parts/GdxTableEditDialog';

export default function RemoteTable({ children, options }) {
  options.columns = options.columns.filter((x) => !x.notShow);
  return (
    <RemoteTableProvider options={options}>
      <Card sx={{ background: options.background || '' }}>
        <GdxTableStatusBar />
        <GdxTableToolbar />
        <GdxFilterResults />
        <GdxTableContainer />
        <GdxTableFooter />
      </Card>
      {children}
      <GdxTableEditDialog />
    </RemoteTableProvider>
  );
}
