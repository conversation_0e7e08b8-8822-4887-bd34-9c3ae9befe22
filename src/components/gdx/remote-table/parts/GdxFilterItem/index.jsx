import { useMemo, useState, useEffect, useCallback, cloneElement } from 'react';

import { useRemoteTableContext } from '../../context/hook';

export default function GdxFilterItem({ name, children }) {
  const {
    filter,
    updateFilter,
    options: { size },
  } = useRemoteTableContext();
  const [value, setValue] = useState();
  useEffect(() => {
    if (!filter) return;
    const v = Reflect.get(filter, name);
    setValue(v);
  }, [name, filter, updateFilter]);
  const onChange = useCallback(
    (v) => {
      if (v && v.target && v._reactName) {
        v = v.target.value;
      }
      if (v !== undefined) {
        // 创建新的 filter 对象，避免直接修改原对象
        const newFilter = { ...filter, [name]: v };
        updateFilter(newFilter);
      }
    },
    [name, filter, updateFilter]
  );
  const ele = useMemo(() => {
    const props = { onChange };
    if (value !== undefined) {
      props.value = value;
    }
    if (size) {
      props.size = size;
    }
    return cloneElement(children, props);
  }, [value, onChange]);

  return ele;
}
