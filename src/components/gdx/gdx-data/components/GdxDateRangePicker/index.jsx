import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useState, useEffect } from 'react';

import { DateRangePicker, SingleInputDateRangeField } from '@mui/x-date-pickers-pro';

import { useCountryContext } from '../../context/country';

dayjs.extend(utc);
dayjs.extend(timezone);

export default function GdxDateRangePicker({ label, value, onChange, ...restProps }) {
  const { timezone: tz } = useCountryContext();
  const [v, setV] = useState([null, null]);
  useEffect(() => {
    if (value) {
      setV([dayjs.tz(value.start, tz).startOf('day'), dayjs.tz(value.finish, tz).startOf('day')]);
    } else {
      setV([null, null]);
    }
  }, [value, tz]);
  return (
    <DateRangePicker
      label={label}
      value={v}
      onChange={(nv) => {
        if (onChange) {
          onChange(
            nv && nv[0] && nv[1]
              ? { start: nv[0].startOf('day').valueOf(), finish: nv[1].startOf('day') }
              : null
          );
        }
      }}
      timezone={tz}
      slots={{ field: SingleInputDateRangeField }}
      slotProps={{
        field: { clearable: true },
      }}
      sx={{
        maxWidth: { xs: 1, md: 360 },
        width: { xs: 1, md: 360 },
      }}
      {...restProps}
    />
  );
}
