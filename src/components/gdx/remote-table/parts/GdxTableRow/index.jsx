import { merge } from 'lodash';
import { toast } from 'sonner';
import { useMemo, useState, useEffect, useCallback } from 'react';

import {
  Button,
  Divider,
  Checkbox,
  Collapse,
  MenuItem,
  TableRow,
  TableCell,
  IconButton,
  ButtonGroup,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { usePopover } from 'src/components/custom-popover';
import GdxValue from 'src/components/gdx/gdx-data/components/GdxValue';
import { CustomPopover } from 'src/components/custom-popover/custom-popover';
import { useGdxDataContext } from 'src/components/gdx/gdx-data/context/hook';
import { useLangTranslate } from 'src/components/gdx/gdx-data/context/common';

import { useRemoteTableContext } from '../../context/hook'; // eslint-disable-line import/no-cycle

export default function GdxTableRow({ row, rowIndex }) {
  const { rl } = useLangTranslate();
  const { showConfirm } = useGdxDataContext();
  const [actionDatas, setActionDatas] = useState([]);
  const context = useRemoteTableContext();
  const {
    isSelected,
    setSelectedRowId,
    deleteOne,
    setSelectedRowIds,
    moveUp,
    selectedRowIds,
    setEditAction,
    reload,
    expandIds,
    setExpandIds,
    options: {
      columns,
      batchActions,
      expandRowRender,
      canExpandRow,
      actions,
      onAction,
      size,
      dataSource,
    },
  } = context;
  const popover = usePopover();
  const onActionWrap = useCallback(
    async (act) => {
      if (onAction) {
        if (await onAction(act, row, context)) {
          return;
        }
      }
      if (act.id === 'delete') {
        showConfirm({
          title: rl('Delete'),
          content: rl('Are you sure want to delete?'),
          okText: rl('Delete'),
          okColor: 'error',
          onOk: async () => {
            const resp = await deleteOne(row.id, rowIndex);
            if (resp.status === 0) {
              // enqueueSnackbar(`${rl('Delete success')}!`);
              toast.success(`${rl('Delete success')}!`);
              reload();
              setSelectedRowIds(selectedRowIds || []);
            }
          },
        });
      } else if (act.id === 'edit') {
        setEditAction('edit', merge({}, row, { _rowIndex: rowIndex }));
      } else if (act.id === 'copy') {
        setEditAction(
          'copy',
          merge({}, row, { id: null, copyFormIdXX: row.id, _rowIndex: rowIndex })
        );
      } else if (act.id === 'up') {
        moveUp(act.id, rowIndex);
      }
    },
    [row, selectedRowIds]
  );
  useEffect(() => {
    let actList;
    if (!actions) {
      setActionDatas([]);
      return;
    }
    if (typeof actions === 'function') {
      actList = actions({ row, context });
    } else {
      actList = actions;
    }
    const acts = actList.map((x) => {
      if (x === 'delete') {
        x = {
          id: 'delete',
          icon: 'solar:trash-bin-trash-bold',
          label: rl('Delete'),
          sx: { color: 'error.main' },
        };
      } else if (x === 'edit') {
        x = { id: 'edit', icon: 'solar:pen-bold', label: rl('Edit') };
      } else if (x === 'divider') {
        return x;
      } else if (x === 'copy') {
        x = { id: 'copy', icon: 'solar:copy-bold', label: rl('Copy') };
      } else if (x === 'up') {
        x = { id: 'up', icon: 'icon-park-solid:up-two', label: rl('Up') };
      }
      return x;
    });
    setActionDatas(acts || []);
  }, [actions, row]);
  const expandRowFlag = (expandRowRender && (!canExpandRow || canExpandRow(row)) && true) || false;
  const collapse = useMemo(
    () => ({
      value: expandIds.indexOf(row.id) >= 0,
      onToggle: () => {
        if (expandIds.indexOf(row.id) >= 0) {
          setExpandIds(expandIds.filter((x) => x !== row.id));
        } else {
          setExpandIds([].concat(expandIds, row.id));
        }
      },
    }),
    [expandIds]
  );
  return (
    <>
      <TableRow hover selected={isSelected(row.id)}>
        {(batchActions && batchActions.length && (
          <TableCell padding="checkbox">
            <Checkbox
              checked={isSelected(row.id)}
              onChange={() => {
                setSelectedRowId(row.id);
              }}
            />
          </TableCell>
        )) ||
          null}

        {columns.map((col) => {
          let v = Reflect.get(row, col.id);
          if (col.id.indexOf('.') !== -1) {
            const r = Reflect.get(row, col.id.split('.')[0]);
            v = Reflect.get(r, col.id.split('.')[1]);
          }
          if (col.render) {
            v = col.render({ value: v, row, index: rowIndex });
          } else if (col.type) {
            v = (
              <GdxValue
                dataSource={dataSource}
                type={col.type}
                value={v}
                row={row}
                rowIndex={rowIndex}
                {...col.typeProps}
              ></GdxValue>
            );
          }
          if (col.align === 'center') {
            v = (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                {v}
              </div>
            );
          } else if (col.align === 'right') {
            v = (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                {v}
              </div>
            );
          }
          return (
            <TableCell key={col.id} align={col.align} width={col.width}>
              {v}
            </TableCell>
          );
        })}

        {(((actions && actions.length) || expandRowRender) && (
          <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
            <ButtonGroup variant="contained">
              {expandRowFlag && (
                <Button
                  size={size}
                  variant="outlined"
                  color={collapse.value ? 'primary' : 'inherit'}
                  onClick={collapse.onToggle}
                  sx={{
                    ...(collapse.value && {
                      bgcolor: 'action.hover',
                    }),
                  }}
                  endIcon={
                    <Iconify
                      icon={
                        collapse.value ? 'eva:arrow-ios-upward-fill' : 'eva:arrow-ios-downward-fill'
                      }
                    />
                  }
                >
                  {collapse.value ? rl('Collapse') : rl('Expand')}
                </Button>
              )}

              {(actionDatas && actionDatas.length && (
                <>
                  {actionDatas
                    .filter((x) => x.quick)
                    .map((x) => {
                      const { id, icon, label, ...restProps } = x;
                      if (label) {
                        return (
                          <Button
                            key={id}
                            variant="contained"
                            size={size}
                            endIcon={<Iconify icon={icon}></Iconify>}
                            style={{ margin: '0 4px' }}
                            onClick={async () => {
                              await onActionWrap(x);
                            }}
                            {...{ ...restProps, quick: undefined }}
                          >
                            {label}
                          </Button>
                        );
                      }
                      return (
                        <IconButton
                          size={size}
                          key={id}
                          onClick={async () => {
                            await onActionWrap(x);
                          }}
                          {...restProps}
                        >
                          <Iconify icon={icon}></Iconify>
                        </IconButton>
                      );
                    })}
                  {(actionDatas.filter((x) => !x.quick).length && (
                    <IconButton
                      size={size}
                      color={popover.open ? 'inherit' : 'default'}
                      onClick={popover.onOpen}
                    >
                      <Iconify icon="eva:more-vertical-fill" />
                    </IconButton>
                  )) ||
                    null}
                </>
              )) ||
                null}
            </ButtonGroup>
          </TableCell>
        )) ||
          null}
      </TableRow>

      {expandRowFlag && (
        <TableRow>
          <TableCell
            sx={{ p: 0, border: 'none' }}
            colSpan={batchActions && batchActions.length ? columns.length + 2 : columns.length + 1}
          >
            <Collapse in={collapse.value} timeout="auto" sx={{ bgcolor: 'background.neutral' }}>
              {collapse.value && expandRowRender(row, rowIndex)}
            </Collapse>
          </TableCell>
        </TableRow>
      )}

      <CustomPopover
        open={popover.open}
        onClose={popover.onClose}
        anchorEl={popover.anchorEl}
        slotProps={{
          arrow: {
            placement: 'right-top',
          },
          paper: {
            sx: {
              ml: -3,
            },
          },
        }}
        sx={{ width: 160 }}
      >
        {actionDatas
          .filter((x) => !x.quick)
          .map((ad, index) => {
            if (ad === 'divider') {
              return <Divider key={index} sx={{ borderStyle: 'dashed' }} />;
            }
            const { icon, label, ...restProps } = ad;
            return (
              <MenuItem
                key={index}
                onClick={async () => {
                  onActionWrap(ad);
                  popover.onClose();
                }}
                {...{ ...restProps, quick: undefined }}
              >
                <Iconify icon={icon} />
                {label}
              </MenuItem>
            );
          })}
      </CustomPopover>
    </>
  );
}
