import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { AuthLayout } from 'src/layouts/auth';

import { SplashScreen } from 'src/components/loading-screen';

const SignInPage = lazy(() => import('src/pages/auth/jwt/sign-in'));

const authCentered = {
  path: 'jwt',
  element: (
    <AuthLayout>
      <Outlet />
    </AuthLayout>
  ),
  children: [{ path: 'sign-in', element: <SignInPage /> }],
};

// ----------------------------------------------------------------------

export const authRoutes = [
  {
    path: 'auth',
    element: (
      <Suspense fallback={<SplashScreen />}>
        <Outlet />
      </Suspense>
    ),
    children: [authCentered],
  },
];
