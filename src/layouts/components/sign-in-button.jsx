import Button from '@mui/material/Button';

import { RouterLink } from 'src/routes/components';

import { CONFIG } from 'src/config-global';

// ----------------------------------------------------------------------

export function SignInButton({ sx, ...other }) {
  return (
    <Button
      component={RouterLink}
      href={CONFIG.site.redirectPath}
      variant="outlined"
      sx={sx}
      {...other}
    >
      登录
    </Button>
  );
}
