import { Box, Card, CardContent, Pagination, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import GdxImage from 'src/components/gdx/gdx-data/components/GdxImage';
import request from 'src/utils/request';

export default function YxLoveFinishCards() {
  const [list, setList] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  useEffect(() => {
    const fetchData = async () => {
      const resp = await request('/api/love/page', {
        query: {
          pageNo,
          pageSize: 20,
          status: 'FINISHED',
          sortOrder: -1,
          sortField: 'finishTime',
        },
      });
      if (resp.status === 0) {
        setList(resp.page.content);
        setTotalPages(resp.page.totalPages);
      }
    };
    fetchData();
  }, [pageNo]);
  return (
    <Stack spacing={1}>
      {list.map((item, index) => (
        <YxLoveFinishCard key={index} item={item} />
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
        <Pagination
          count={totalPages}
          color="primary"
          onChange={(e, page) => setPageNo(page)}
          showFirstButton
          showLastButton
        />
      </Box>
    </Stack>
  );
}

function YxLoveFinishCard({ item }) {
  const { title, yLog, xLog, photos, finishTime } = item;
  return (
    <Card>
      <CardContent>
        <Typography variant="h5" color="pink">
          {title}
        </Typography>
        <Typography variant="body2" gutterBottom color="text.secondary">
          {dayjs(finishTime).format('YYYY-MM-DD')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小徐：{yLog}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小张：{xLog}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <GdxImage value={photos} />
        </Box>
      </CardContent>
    </Card>
  );
}
